import math
import random
from typing import Generator, List, Union

import torch

from utils.dataloader import Dataloader


class Data:
    n_primary_bars: int
    n_secondary_bars: int
    n_features: int
    n_symbols: int

    n_step: int
    n_backward_bars: int
    n_forward_bars: int

    indexes: List[int]

    def __init__(
        self,
        index,
        features: List[str],
        start_time,
        end_time,
        freq: str,
        backward_bars: int,
        forward_bars: int,
        step: int = 0,
        sample_rate: float = 1.0,
        device: str = "cpu",
    ):
        self.device = device

        self.features = [f.lower() for f in features]
        self.freq = freq
        self.step = step
        self.backward_bars = backward_bars
        self.forward_bars = forward_bars
        self.sample_rate = 1.0 if self.freq.endswith("d") else sample_rate

        self.data = Dataloader.prepare_data(
            index=index,
            start_time=start_time,
            end_time=end_time,
            freq=freq,
            fields=self.features,
            max_backtrack_bars=backward_bars,
            max_future_bars=forward_bars,
        )
        self.set_shape()

        assert 0 < self.sample_rate <= 1
        self.resample()

        self.data = self.data.reshape(-1, self.n_features, self.n_symbols)
        assert self.data.shape[0] > 2 * self.backward_bars

    def set_shape(self):
        self.n_primary_bars, self.n_secondary_bars, self.n_features, self.n_symbols = self.data.shape
        if self.freq.endswith("m"):
            self.n_step = self.step or self.n_primary_bars
            backward_primary_bars = math.ceil(self.backward_bars / self.n_secondary_bars)
            self.n_backward_bars = backward_primary_bars * self.n_secondary_bars
            forward_primary_bars = math.ceil(self.forward_bars / self.n_secondary_bars)
            self.n_forward_bars = forward_primary_bars * self.n_secondary_bars
            self.n_primary_bars -= backward_primary_bars + forward_primary_bars
        else:
            self.n_step = self.n_primary_bars
            self.sample_rate = 1.0
            self.n_backward_bars = self.backward_bars
            self.n_forward_bars = self.forward_bars
            self.n_secondary_bars -= self.backward_bars + self.forward_bars

    def unfold_period(self, period: slice, index: int):
        step = min(self.n_step, self.n_primary_bars - index)
        start = index * self.n_secondary_bars + period.start + self.backward_bars
        stop = (index + step) * self.n_secondary_bars + period.stop + self.backward_bars
        return start, stop - 1

    def resample(self):
        self.indexes = list(range(0, self.n_primary_bars, self.n_step))
        if self.sample_rate < 1:
            sample_size = math.ceil(len(self.indexes) * self.sample_rate)
            self.indexes = random.sample(self.indexes, sample_size)

    def __call__(self, period: slice, tag: Union[str, float]) -> Generator[torch.Tensor, None, None]:
        assert period.start >= -self.n_backward_bars
        assert period.stop - 1 <= self.n_forward_bars

        for i in self.indexes:
            start, stop = self.unfold_period(period, i)
            if isinstance(tag, str):
                yield self.data[start:stop, self.features.index(tag.lower()), :].to(self.device)
            elif isinstance(tag, float):
                yield torch.full((stop - start, self.n_symbols), tag, dtype=self.data.dtype, device=self.device)
            else:
                raise TypeError(f"{type(tag)} is not support")
