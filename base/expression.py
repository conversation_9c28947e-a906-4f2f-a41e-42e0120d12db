from enum import IntEnum
from typing import Generator, List, Optional, Set, Union

import torch

from base.data import Data


class SlotType(IntEnum):
    NotDefine = 0
    Feature = 1
    Constant = 2
    Bar = 3


class Expression:
    value: Union[str, float, int]

    def __init__(self, *args): ...

    def __call__(self, *args, **kwargs):
        raise NotImplementedError

    def __repr__(self):
        return str(self)

    def __hash__(self):
        return hash(str(self))

    def __add__(self, other):
        return Add(self, other)

    def __radd__(self, other):
        return Add(other, self)

    def __sub__(self, other):
        return Sub(self, other)

    def __rsub__(self, other):
        return Sub(other, self)

    def __mul__(self, other):
        return Mul(self, other)

    def __rmul__(self, other):
        return Mul(other, self)

    def __truediv__(self, other):
        return Div(self, other)

    def __rtruediv__(self, other):
        return Div(other, self)

    def __neg__(self):
        return Neg(self)

    def __pow__(self, other):
        return Pow(self, other)

    def __rpow__(self, other):
        return Pow(other, self)

    def __eq__(self, other: "Expression"):
        return str(self) == str(other)

    @property
    def is_feature(self) -> bool:
        return False

    @property
    def period(self):
        return slice(0, 1)

    @property
    def slot_type(self) -> SlotType:
        return SlotType.NotDefine

    @property
    def units(self):
        return []


class Operand(Expression):
    def __init__(self, value):
        super().__init__()
        self.value = value

    @property
    def units(self):
        return [self.value]


class Feature(Operand):
    value: str

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        return data(period, self.value)

    def __str__(self):
        return f"${self.value}"

    @property
    def is_feature(self):
        return True

    @property
    def slot_type(self) -> SlotType:
        return SlotType.Feature


class Constant(Operand):
    value: float

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        return data(period, float(self.value))

    def __str__(self):
        return str(self.value)

    @property
    def slot_type(self) -> SlotType:
        return SlotType.Constant


class Bar(Operand):
    value: int

    def __str__(self):
        return str(self.value)

    @property
    def slot_type(self) -> SlotType:
        return SlotType.Bar


class Operator(Expression):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return []

    @classmethod
    def n_args(cls) -> int:
        if not cls.slots():
            return 0
        return len(cls.slots()[0])

    @classmethod
    def check_args(cls, args: List[Expression]) -> bool:
        arg_slots = [arg.slot_type for arg in args]
        for slots in cls.slots():
            if arg_slots == slots[: len(arg_slots)]:
                return True
        return False

    @classmethod
    def require(cls) -> Set[str]:
        return set()

    @property
    def slot_type(self) -> SlotType:
        if self.is_feature:
            return SlotType.Feature
        else:
            return SlotType.Constant


"""
Unary Operators
"""


class UnaryOperator(Operator):
    def __init__(self, feature: Expression):
        super().__init__()
        self.feature = feature

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature]]

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        for value in self.feature(data, period):
            yield self.apply(value)

    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.feature})"

    @property
    def is_feature(self):
        return self.feature.is_feature

    @property
    def units(self):
        return [*self.feature.units, type(self)]


"""
Arithmetic Unary Operators
"""


class Neg(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.neg()


class Abs(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.abs()


class Inv(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.reciprocal()


class Sign(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sign()


class Sqrt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sqrt()


class UnsignedSqrt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sqrt.apply(Abs.apply(value))


class SignedSqrt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sign.apply(value) * UnsignedSqrt.apply(value)


class Square(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.square()


class Curt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.pow(1 / 3)


class UnsignedCurt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Curt.apply(Abs.apply(value))


class SignedCurt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sign.apply(value) * UnsignedCurt.apply(value)


class Cube(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.pow(3)


class Log(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.log()


class UnsignedLog(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Log.apply(Abs.apply(value))


class SignedLog(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return Sign.apply(value) * UnsignedLog.apply(value)


class Exp(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.exp()


"""
Non-Linear Unary Operators
"""


class TanH(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.tanh()


class Sigmoid(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.sigmoid()


class ReLU(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.relu()


class GeLU(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return torch.nn.functional.gelu(value)


"""
Statistic Unary Operators
"""


class UnaryCount(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingCount.apply(value).unsqueeze(-1).expand_as(value)


class UnarySum(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingSum.apply(value).unsqueeze(-1).expand_as(value)


class UnaryProd(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingProd.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMean(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMean.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMed(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMed.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMad(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMad.apply(value).unsqueeze(-1).expand_as(value)


class UnaryVar(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingVar.apply(value).unsqueeze(-1).expand_as(value)


class UnaryStd(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingStd.apply(value).unsqueeze(-1).expand_as(value)


class UnaryIncv(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingIncv.apply(value).unsqueeze(-1).expand_as(value)


class UnarySkew(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingSkew.apply(value).unsqueeze(-1).expand_as(value)


class UnaryKurt(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingKurt.apply(value).unsqueeze(-1).expand_as(value)


class UnaryRank(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        count = UnaryCount.apply(value)
        value = FiniteCond.apply(value, value, torch.inf).unsqueeze(-1)
        lesser = (value < value.transpose(-1, -2)).sum(dim=-1)
        equal = (value == value.transpose(-1, -2)).sum(dim=-1)
        rank = lesser + (equal - 1) / 2
        return rank / (count - 1)


class UnaryDescendRank(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        count = UnaryCount.apply(value)
        value = FiniteCond.apply(value, value, torch.inf).unsqueeze(-1)
        greater = (value > value.transpose(-1, -2)).sum(dim=-1)
        equal = (value == value.transpose(-1, -2)).sum(dim=-1)
        rank = greater + (equal - 1) / 2
        return rank / (count - 1)


class UnaryMax(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMax.apply(value).unsqueeze(-1).expand_as(value)


class UnaryMin(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return RollingMin.apply(value).unsqueeze(-1).expand_as(value)


"""
Unary Norm Operators 
"""


class UnaryCentral(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value - value.nanmean(dim=-1, keepdim=True)


class UnaryZScoreNorm(UnaryCentral):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return UnaryCentral.apply(value) / UnaryStd.apply(value)


class UnaryL1Norm(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value / UnarySum.apply(value)


class UnaryL2Norm(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value / Sqrt.apply(UnarySum.apply(value**2))


class UnaryMinMaxNorm(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        min_value = UnaryMin.apply(value)
        return (value - min_value) / (UnaryMax.apply(value) - min_value)


class UnarySoftmax(UnaryOperator):
    @staticmethod
    def apply(value: torch.Tensor) -> torch.Tensor:
        return value.softmax(dim=-1)


"""
Binary Operators
"""


class BinaryOperator(Operator):
    def __init__(self, left: Union[Expression, float], right: Union[Expression, float]):
        super().__init__()
        self.left = left if isinstance(left, Expression) else Constant(left)
        self.right = right if isinstance(right, Expression) else Constant(right)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [
            [SlotType.Feature, SlotType.Constant],
            [SlotType.Constant, SlotType.Feature],
            [SlotType.Feature, SlotType.Feature],
        ]

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        for left, right in zip(self.left(data, period), self.right(data, period)):
            yield self.apply(left, right)

    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.left},{self.right})"

    @property
    def is_feature(self):
        return self.left.is_feature or self.right.is_feature

    @property
    def units(self):
        return [*self.left.units, *self.right.units, type(self)]


"""
Arithmetic Binary Operators
"""


class Add(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left + right


class Sub(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left - right


class Mul(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left * right


class Div(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left / right


class Pow(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left.pow(right)


class UnsignedPow(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Pow.apply(Abs.apply(left), right)


class SignedPow(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Sign.apply(left) * UnsignedPow.apply(left, right)


class BinaryLog(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Log.apply(left) / Log.apply(right)


class UnsignedBinaryLog(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Log.apply(Abs.apply(left)) / Log.apply(Abs.apply(right))


class SignedBinaryLog(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return Sign.apply(left) * UnsignedBinaryLog.apply(left, right)


"""
Compare Binary Operator
"""


class BinaryMax(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left.max(right)


class BinaryMin(BinaryOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: Union[torch.Tensor, float]) -> torch.Tensor:
        return left.min(right)


"""
Ternary Operators
"""


class TernaryOperator(Operator):
    def __init__(self, condition: Expression, left: Union[Expression, float], right: Union[Expression, float]):
        super().__init__()
        self.condition = condition
        self.left = left if isinstance(left, Expression) else Constant(left)
        self.right = right if isinstance(right, Expression) else Constant(right)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [
            [SlotType.Feature, SlotType.Feature, SlotType.Constant],
            [SlotType.Feature, SlotType.Constant, SlotType.Feature],
            [SlotType.Feature, SlotType.Feature, SlotType.Feature],
        ]

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        for cond, left, right in zip(self.condition(data, period), self.left(data, period), self.right(data, period)):
            yield self.apply(cond, left, right)

    @staticmethod
    def apply(
        cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]
    ) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.condition},{self.left},{self.right})"

    @property
    def is_feature(self):
        return self.condition.is_feature or self.left.is_feature or self.right.is_feature

    @property
    def units(self):
        return [*self.condition.units, *self.left.units, *self.right.units, type(self)]


class PositiveCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond > 0, left, right)


class NegativeCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond < 0, left, right)


class FiniteCond(TernaryOperator):
    @staticmethod
    def apply(cond: torch.Tensor, left: Union[torch.Tensor, float], right: Union[torch.Tensor, float]) -> torch.Tensor:
        return torch.where(cond.isfinite(), left, right)


"""
Rolling Operators
"""


class RollingOperator(Operator):
    def __init__(self, feature: Expression, bar: Union[int, Bar]):
        super().__init__()
        self.feature = feature
        self.bar = bar if isinstance(bar, Bar) else Bar(bar)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Bar]]

    @classmethod
    def check_args(cls, args: List[Expression]) -> bool:
        if not super().check_args(args):
            return False
        if len(args) > 1 and args[1].value < 2:
            return False
        return True

    def _expand_period(self, period: slice = slice(0, 1)):
        if self.bar.value > 0:
            return slice(period.start - self.bar.value + 1, period.stop)
        else:
            return slice(period.start, period.stop - self.bar.value - 1)

    def _unfold_value(self, value: torch.Tensor):
        return value.unfold(0, abs(self.bar.value), 1)

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        period = self._expand_period(period)
        for value in self.feature(data, period):
            value = self._unfold_value(value)
            yield self.apply(value, self.bar.value)

    @staticmethod
    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
        return value

    def __str__(self):
        return f"{type(self).__name__}({self.feature},{self.bar})"

    @property
    def is_feature(self):
        return self.feature.is_feature

    @property
    def period(self):
        return self._expand_period(self.feature.period)

    @property
    def units(self):
        return [*self.feature.units, *self.bar.units, type(self)]


"""
Statistic Rolling Operator
"""


class RollingCount(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, 1.0, 0.0).sum(dim=-1)


class RollingSum(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value.nansum(dim=-1)


class RollingProd(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, 1.0).prod(dim=-1)


class RollingMean(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value.nanmean(dim=-1)


class RollingMed(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value.nanmedian(dim=-1)[0]


class RollingMad(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return RollingMean.apply(Abs.apply(UnaryCentral.apply(value)))


class RollingVar(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return RollingSum.apply(UnaryCentral.apply(value) ** 2) / (RollingCount.apply(value) - 1)


class RollingStd(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Sqrt.apply(RollingVar.apply(value))


class RollingIncv(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return RollingMean.apply(value) / RollingStd.apply(value)


class RollingSkew(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        top = RollingMean.apply(UnaryCentral.apply(value) ** 3)
        bottom = RollingMean.apply(UnaryCentral.apply(value) ** 2) ** 1.5
        return top / bottom


class RollingKurt(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        top = RollingMean.apply(UnaryCentral.apply(value) ** 4)
        bottom = RollingVar.apply(value) ** 2
        return top / bottom - 3


class RollingMax(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, -torch.inf).amax(dim=-1)


class RollingMin(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, torch.inf).amin(dim=-1)


class Argmax(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, -torch.inf).argmax(dim=-1).to(value.dtype)


class Argmin(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return FiniteCond.apply(value, value, torch.inf).argmin(dim=-1).to(value.dtype)


class ArgmaxArgmin(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Argmax.apply(value) - Argmin.apply(value)


"""
Rolling Norm Operators
"""


class RollingRank(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryRank.apply(value)[..., 0]
        else:
            return UnaryRank.apply(value)[..., -1]


class RollingDescendRank(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryDescendRank.apply(value)[..., 0]
        else:
            return UnaryDescendRank.apply(value)[..., -1]


class RollingCentral(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: int) -> torch.Tensor:
        if bar < 0:
            return UnaryCentral.apply(value)[..., 0]
        else:
            return UnaryCentral.apply(value)[..., -1]


class RollingZScoreNorm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryZScoreNorm.apply(value)[..., 0]
        else:
            return UnaryZScoreNorm.apply(value)[..., -1]


class RollingL1Norm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryL1Norm.apply(value)[..., 0]
        else:
            return UnaryL1Norm.apply(value)[..., -1]


class RollingL2Norm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryL2Norm.apply(value)[..., 0]
        else:
            return UnaryL2Norm.apply(value)[..., -1]


class RollingMinMaxNorm(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnaryMinMaxNorm.apply(value)[..., 0]
        else:
            return UnaryMinMaxNorm.apply(value)[..., -1]


class RollingSoftmax(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return UnarySoftmax.apply(value)[..., 0]
        else:
            return UnarySoftmax.apply(value)[..., -1]


"""
Weighted Rolling Operator
"""


class DecayLinear(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        weights = torch.arange(value.shape[-1], 0, -1, device=value.device, dtype=value.dtype).expand_as(value)
        weights /= RollingSum.apply(FiniteCond.apply(value, weights, 0))
        return RollingSum.apply(value * weights)


class DescendDecayLinear(RollingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        weights = torch.arange(value.shape[-1], device=value.device, dtype=value.dtype).expand_as(value) + 1
        weights /= RollingSum.apply(FiniteCond.apply(value, weights, 0))
        return RollingSum.apply(value * weights)


"""
Shifting Operator
"""


class ShiftingOperator(RollingOperator):
    def _expand_period(self, period: slice = slice(0, 1)):
        if self.bar.value > 0:
            return slice(period.start - self.bar.value, period.stop)
        else:
            return slice(period.start, period.stop - self.bar.value)

    def _unfold_value(self, value: torch.Tensor):
        return value.unfold(0, abs(self.bar.value) + 1, 1)


class Ref(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        if bar < 0:
            return value[..., -1]
        else:
            return value[..., 0]


class Delta(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value[..., -1] - value[..., 0]


class Ratio(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return value[..., -1] / value[..., 0]


class DeltaRatio(ShiftingOperator):
    @staticmethod
    def apply(value: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Ratio.apply(value) - 1


"""
Statistic Rolling Operator
"""


class PairRollingOperator(Operator):
    def __init__(self, left: Expression, right: Expression, bar: Union[int, Bar]):
        super().__init__()
        self.left = left
        self.right = right
        self.bar = bar if isinstance(bar, Bar) else Bar(bar)

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Feature, SlotType.Bar]]

    def _expand_period(self, period: slice = slice(0, 1)):
        if self.bar.value > 0:
            return slice(period.start - self.bar.value + 1, period.stop)
        else:
            return slice(period.start, period.stop - self.bar.value - 1)

    def _unfold_value(self, value: torch.Tensor):
        return value.unfold(0, abs(self.bar.value), 1)

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        period = self._expand_period(period)
        for left, right in zip(self.left(data, period), self.right(data, period)):
            left = self._unfold_value(left)
            right = self._unfold_value(right)
            yield self.apply(left, right, self.bar.value)

    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor: ...

    def __str__(self):
        return f"{type(self).__name__}({self.left},{self.right},{self.bar})"

    @property
    def is_feature(self):
        return self.left.is_feature or self.right.is_feature

    @property
    def period(self):
        left_period = self._expand_period(self.left.period)
        right_period = self._expand_period(self.right.period)
        return slice(min(left_period.start, right_period.start), max(left_period.stop, right_period.stop))

    @property
    def units(self):
        return [*self.left.units, *self.right.units, *self.bar.units, type(self)]


class Cov(PairRollingOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        top = RollingSum.apply(UnaryCentral.apply(left) * UnaryCentral.apply(right))
        bottom = UnaryCount.apply(top) - 1
        return top / bottom


class Corr(PairRollingOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        return Cov.apply(left, right) / (RollingStd.apply(left) * RollingStd.apply(right))


class PairSlope(PairRollingOperator):
    @staticmethod
    def apply(left: torch.Tensor, right: torch.Tensor, bar: Optional[int] = None) -> torch.Tensor:
        left_central = UnaryCentral.apply(left)
        top = RollingSum.apply(left_central * UnaryCentral.apply(right))
        bottom = RollingSum.apply(left_central**2)
        return top / bottom


class CompositeOperator(Operator):
    features: List[Union[Operator, Feature]]
    formulas: List[Union[Operator, Feature]]
    args: List[Union[int, float]]

    def __init__(self, *args):
        super().__init__()
        self.features, self.args = [], []
        for arg in args:
            if isinstance(arg, (Feature, Operator)):
                self.features.append(arg)
            elif isinstance(arg, Operand):
                self.args.append(arg.value)
            else:
                self.args.append(arg)
        self.formulas = self.features

    def __call__(self, data: Data, period: slice = slice(0, 1)) -> Generator[torch.Tensor, None, None]:
        last_value = None
        for values in zip(*[formula(data, period) for formula in self.formulas]):
            yield self.apply(list(values), self.args, last_value)

    def __str__(self):
        return f"{type(self).__name__}({','.join(str(e) for e in self.features + self.args)})"

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor: ...

    @property
    def is_feature(self) -> bool:
        return any(feature.is_feature for feature in self.formulas)


class SMA(CompositeOperator):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Constant, SlotType.Constant]]

    @classmethod
    def check_args(cls, args: List[Expression]) -> bool:
        if not super().check_args(args):
            return False
        if len(args) > 1 and args[1].value < 0:
            return False
        if len(args) > 2:
            if args[2].value < 0:
                return False
            if args[1].value < args[2].value:
                return False
        return True

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        value, (alpha, beta) = values[0], args
        if last_value is None:
            x_last = value[0]
        else:
            x_last = last_value[-1]
        for i, x in enumerate(value):
            value[i] = x_last = (x_last * (beta - alpha) + x * alpha) / beta
        return value


class EMA(CompositeOperator):
    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Feature, SlotType.Constant]]

    @classmethod
    def check_args(cls, args: List[Expression]) -> bool:
        if not super().check_args(args):
            return False
        if args:
            if args[-1].value > 1:
                return False
            if args[-1].value < 0:
                return False
        return True

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        value, weight = values[0], args[0]
        if last_value is None:
            x_last = value[0]
        else:
            x_last = last_value[-1]
        for i, x in enumerate(value):
            value[i] = x_last = x_last * (1 - weight) + x * weight
        return value


class RSI(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [RollingOperator(DeltaRatio(Feature("close"), 1), self.args[0])]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"close"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        value = values[0]
        up_average = RollingMean.apply(PositiveCond.apply(value, value, torch.nan))
        down_average = RollingMean.apply(NegativeCond.apply(value, Abs.apply(value), torch.nan))
        rs = up_average / down_average
        return rs / (rs + 1)


class RSV(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            Feature("close"),
            RollingMax(Feature("high"), self.args[0]),
            RollingMin(Feature("low"), self.args[0]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"close", "high", "low"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        close, high, low = values
        return (close - low) / (high - low)


class MACD(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [EMA(Feature("close"), 2 / (arg + 1)) for arg in self.args]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar, SlotType.Bar, SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"close"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        dif = values[0] - values[1]
        dea = values[2]
        return dif - dea


class KDJ(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            SMA(RSV(self.args[0]), self.args[1], 1),
            SMA(RSV(self.args[0]), self.args[2], 1),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar, SlotType.Bar, SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return SMA.require()

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        k, d = values
        return 3 * k - 2 * d


class WR(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [
            Feature("close"),
            RollingMax(Feature("high"), self.args[0]),
            RollingMin(Feature("low"), self.args[0]),
        ]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def check_args(cls, args: List[Expression]) -> bool:
        if not super().check_args(args):
            return False
        return RollingMax.check_args(args) and RollingMin.check_args(args)

    @classmethod
    def require(cls) -> Set[str]:
        return {"close", "high", "low"}

    @staticmethod
    def apply(
        values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
    ) -> torch.Tensor:
        close, high, low = values
        return (high - close) / (high - low)


class ATR(CompositeOperator):
    def __init__(self, *args):
        super().__init__(*args)
        self.formulas = [Feature("tr")]

    @classmethod
    def slots(cls) -> List[List[SlotType]]:
        return [[SlotType.Bar]]

    @classmethod
    def require(cls) -> Set[str]:
        return {"tr"}

    @staticmethod
    def apply(values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]):
        value, bar = values[0], args[0]
        return RollingMean.apply(value, bar)


# class MFI(CompositeOperator):
#     def __init__(self, *args):
#         super().__init__(*args)
#         self.formulas = [Feature("tp"), Feature("volume")]

#     @classmethod
#     def slots(cls) -> List[List[SlotType]]:
#         return [[SlotType.Bar]]

#     @classmethod
#     def require(cls) -> Set[str]:
#         return {"volume", "tp"}

#     @staticmethod
#     def apply(
#         values: List[torch.Tensor], args: List[Union[int, float]], last_value: Optional[torch.Tensor]
#     ) -> torch.Tensor:
#         tp, volume = values
#         bar = args[0]
#         mf = tp * RollingSum.apply(volume, bar)
