from typing import Union, List, Dict, Tuple

from base.expression import Expression, Operator, Feature, SlotType, Operand
from base.tokens import Token, OperatorToken, OperandToken, FeatureToken, BarToken


def get_all_operators(cls=Expression):
    all_operators = []
    for sub_cls in cls.__subclasses__():
        sub_sub_cls = get_all_operators(sub_cls)
        if not sub_sub_cls and issubclass(sub_cls, Operator):
            all_operators.append(sub_cls)
        all_operators.extend(sub_sub_cls)
    return all_operators


class ExpressionBuilder:
    stack: List[Union[Operand, Operator]]

    max_n_args: int

    operators: List[type[Operator]]
    slots2expr: Dict[Tuple[SlotType, ...], List[type[Operator]]]
    expr2slots: Dict[type[Operator], List[Tuple[SlotType, ...]]]

    def __init__(self, operators: List[type[Operator]] = None):
        self.operators, self.max_n_args = [], 0
        for operator in operators or get_all_operators():
            self.max_n_args = max(self.max_n_args, operator.n_args())
            self.operators.append(operator)
        self.operators = sorted(self.operators, key=lambda x: x.n_args())
        self.reset()

    def reset(self):
        self.stack = []

    @property
    def expr(self) -> Expression:
        if len(self.stack) == 1:
            return self.stack[0]
        else:
            raise ValueError(f"Expected only one tree, got {len(self.stack)}")

    def is_valid(self) -> bool:
        return len(self.stack) == 1 and self.stack[0].is_feature

    def add_token(self, token: Token):
        valid, _ = self.validate_token(token)
        if not valid:
            raise ValueError(f"Token {token} not allowed here, stack: {self.stack}.")

        if isinstance(token, OperandToken):
            self.stack.append(token.operand)
        elif isinstance(token, OperatorToken):
            n_args = token.operator.n_args()
            args = self.stack[-n_args:]
            operator = token.operator(*args)
            self.stack = self.stack[:-n_args] + [operator]
        else:
            assert False

    def validate_token(self, token: Token) -> Tuple[bool, int]:
        if isinstance(token, OperatorToken):
            return self.valid_operator(token)
        elif isinstance(token, OperandToken):
            return self.valid_operands(token)
        else:
            assert False

    def valid_operator(self, token: OperatorToken) -> Tuple[bool, int]:
        n_args = token.operator.n_args()
        if len(self.stack) < n_args:
            return False, -1

        right = self.stack[-n_args:]
        if not token.operator.check_args(right):
            return False, -1

        operator = token.operator(*right)
        return self.check_args(self.stack[:-n_args] + [operator])

    def valid_operands(self, token: OperandToken) -> Tuple[bool, int]:
        return self.check_args(self.stack + [token.operand])


    def check_args(self, args: List[Expression], count: int = 0) -> tuple[bool, int]:
        if not args:
            return True, 0
        if len(args) == 1 and args[0].slot_type == SlotType.Feature:
            return True, 0

        check, min_need_args = False, self.max_n_args + 1
        for operator in self.operators:
            n_args = operator.n_args()
            if len(args) > n_args:
                continue
            if operator.check_args(args):
                check = True
                if len(args) < n_args:
                    min_need_args = min(n_args - len(args) + 1, min_need_args)
                else:
                    min_need_args = 1
        if check:
            return True, count + min_need_args

        for i in range(1, min(self.max_n_args, len(args))):
            left, right = args[:i], args[i:]
            right_check, right_count = self.check_args(right)
            if not right_check:
                continue
            left += [Feature("")]
            if left == args:
                continue
            left_check, left_count = self.check_args(left)
            if left_check:
                return True, count + left_count + right_count

        return False, -1


class ExpressionParser:
    def __init__(self):
        self.str_stack = []
        self.expr_stack = []
        self.is_feature = False
        self.operators = {cls.__name__: cls for cls in get_all_operators(Expression)}

    def parse_operator_str(self):
        expr = self.operators.get("".join(self.str_stack))
        self.str_stack = []
        self.expr_stack.append(expr)

    def parse_operand_str(self):
        if not self.str_stack:
            return
        if self.is_feature:
            expr = Feature("".join(self.str_stack))
            self.expr_stack.append(expr)
            self.is_feature = False
        else:
            value = float("".join(self.str_stack))
            if int(value) == value:
                self.expr_stack.append(int(value))
            else:
                self.expr_stack.append(value)
        self.str_stack = []

    def parse_operator_expr(self):
        args = []
        while self.expr_stack:
            expr = self.expr_stack.pop()
            if isinstance(expr, type):
                self.expr_stack.append(expr(*reversed(args)))
                break
            else:
                args.append(expr)

    def parse(self, expr_str: str) -> Expression:
        for c in expr_str:
            if not c.strip():
                continue
            if c == "$":
                self.is_feature = True
            elif c == "(":
                self.parse_operator_str()
            elif c == ")":
                self.parse_operand_str()
                self.parse_operator_expr()
            elif c == ",":
                self.parse_operand_str()
            else:
                self.str_stack.append(c)

        if len(self.expr_stack) == 1:
            return self.expr_stack[0]
        else:
            raise ValueError(f"Expected only one tree, got {len(self.expr_stack)}")


if __name__ == '__main__':
    from base.expression import Abs, Ref, Div, Add

    add_tokens = [
        FeatureToken("LOW"),
        OperatorToken(Abs),
        BarToken(-10),
        OperatorToken(Ref),
        FeatureToken("HIGH"),
        FeatureToken("CLOSE"),
        OperatorToken(Div),
        OperatorToken(Add),
    ]

    builder = ExpressionBuilder()
    for e in add_tokens:
        builder.add_token(e)

    parser = ExpressionParser()
    print(f'ref: Add(Ref(Abs($low),-10),Div($high,$close))')
    print(f'build: {str(builder.expr)}')
    print(f"parse: {str(parser.parse('Add(Ref(Abs($low),-10),Div($high,$close))'))}")
