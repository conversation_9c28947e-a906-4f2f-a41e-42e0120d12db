from enum import IntEnum
from typing import Type, Union

from base.expression import Bar, Constant, Feature, Operand, Operator


class SpecialTokenType(IntEnum):
    PAD = 0
    SEP = 1


class Token:
    def __repr__(self):
        return str(self)


class OperatorToken(Token):
    def __init__(self, operator: Type[Operator]):
        self.operator = operator

    def __str__(self):
        return self.operator.__name__


class OperandToken(Token):
    operand: Operand
    value: Union[float, int, str]


class FeatureToken(OperandToken):
    def __init__(self, value: str):
        self.operand = Feature(value)
        self.value = value

    def __str__(self):
        return '$' + self.value


class ConstantToken(OperandToken):
    def __init__(self, value: float):
        self.operand = Constant(value)
        self.value = value

    def __str__(self):
        return str(self.value)


class BarToken(OperandToken):
    def __init__(self, value: int):
        self.operand = Bar(value)
        self.value = value

    def __str__(self):
        return str(self.value)


class SpecialToken(Token):
    def __init__(self, tag: SpecialTokenType):
        self.tag = tag

    def __str__(self):
        return self.tag.name


PAD_TOKEN = SpecialToken(SpecialTokenType.PAD)
SEP_TOKEN = SpecialToken(SpecialTokenType.SEP)
