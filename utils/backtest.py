from dataclasses import dataclass
from typing import Optional

import numba as nb
import numpy as np
import torch


@dataclass
class Report:
    count: float = 0.
    total_count: float = 0.
    symbol_count: float = 0.
    total_win_rate: float = 0.
    median_win_rate: float = 0.
    days: float = 0.
    mdd: float = 0.
    positive_return_ratio: float = 0.
    sharpe: float = 0.
    arr: float = 0.
    sortino: float = 0.

    def __getitem__(self, key):
        return getattr(self, key)


@nb.njit
def numpy_ffill(x):
    """
    Numpy version of pandas ffill
    """
    n_times, n_stocks, n_strategies = x.shape
    for j in range(n_stocks):
        for k in range(n_strategies):
            last = 0.0
            for i in range(0, n_times):
                if np.isfinite(x[i, j, k]):
                    last = x[i, j, k]
                x[i, j, k] = last
    return x


@nb.njit
def calc_win_rate(buy_open, sell_close, sell_open, buy_close, ret):
    n_times, n_stocks, n_strategies = buy_open.shape
    win = np.zeros((n_stocks, n_strategies), dtype=np.float64)
    loss = np.zeros((n_stocks, n_strategies), dtype=np.float64)

    sell_close_ = sell_close.copy().astype(np.float64)
    sell_close_[-1, ...] = np.inf
    buy_close_ = buy_close.copy().astype(np.float64)
    buy_close_[-1, ...] = np.inf

    for j in range(n_stocks):
        for k in range(n_strategies):
            for i in range(0, n_times):
                if buy_open[i, j, k] > 0.0:
                    pos1 = buy_open[i, j, k]
                    tmp_ret = 0.0
                    for ii in range(i + 1, n_times):
                        if sell_close_[ii, j, k] > 0.0:
                            tmp = np.minimum(pos1, sell_close_[ii, j, k])
                            pos1 -= tmp
                            sell_close_[ii, j, k] -= tmp
                            tmp_ret += tmp * ret[i:ii, j, k].sum()
                            if pos1 == 0.0:
                                break
                    if tmp_ret > 0:
                        win[j, k] += 1
                    else:
                        loss[j, k] += 1
                if sell_open[i, j, k] > 0.0:
                    pos1 = sell_open[i, j, k]
                    tmp_ret = 0.0
                    for ii in range(i + 1, n_times):
                        if buy_close_[ii, j, k] > 0.0:
                            tmp = np.minimum(pos1, buy_close_[ii, j, k])
                            pos1 -= tmp
                            buy_close_[ii, j, k] -= tmp
                            tmp_ret -= tmp * ret[i:ii, j, k].sum()
                            if pos1 == 0.0:
                                break
                    if tmp_ret > 0:
                        win[j, k] += 1
                    else:
                        loss[j, k] += 1
    return win, loss


def backtest(
    pos_t_tsr: torch.Tensor,
    return_tadd1_tsr: torch.Tensor,
    cost: float,
    freq: str = "1d",
    n_secondary_bars: Optional[int] = None,
    one_way: int = 0,
) -> Report:
    pos_t = pos_t_tsr.double().cpu().numpy()
    if one_way == 1:
        pos_t = np.where(pos_t > 0, pos_t, 0)
    elif one_way == -1:
        pos_t = np.where(pos_t < 0, pos_t, 0)

    return_tadd1 = return_tadd1_tsr.double().cpu().numpy()
    if pos_t.ndim == 2:
        pos_t = pos_t[..., None]

    pos_t[np.isinf(pos_t)] = np.nan
    pos_t = numpy_ffill(pos_t)
    pos_t_1 = np.full_like(pos_t, fill_value=0.0, dtype=np.float64)
    pos_t_1[1:] = pos_t[:-1]

    return_tadd1[~np.isfinite(return_tadd1)] = 0
    return_tadd1 = np.repeat(return_tadd1[..., None], repeats=pos_t.shape[2], axis=2)
    # return_tadd1 = torch.repeat_interleave(return_tadd1[..., None], repeats=pos_t.size(2), dim=2)

    ret_ts_ = pos_t * return_tadd1  # (time, n_stocks, n_strategies)
    ret_ts: np.ndarray = np.zeros_like(ret_ts_)
    ret_ts[1:] = ret_ts_[:-1]

    count = np.full_like(pos_t, fill_value=0.0, dtype=np.float64)

    # 买开
    tmp = (pos_t > 0) & (pos_t > pos_t_1)
    count += tmp
    buy_open = np.where(tmp, pos_t - np.maximum(np.zeros_like(pos_t_1), pos_t_1), 0.0)
    ret_ts -= cost * buy_open

    # 卖平
    tmp = (pos_t_1 > 0) & (pos_t < pos_t_1)
    sell_close = np.where(tmp, pos_t_1 - np.maximum(np.zeros_like(pos_t), pos_t), 0.0)
    ret_ts -= cost * sell_close

    # 卖开
    tmp = (pos_t < 0) & (pos_t < pos_t_1)
    count += tmp
    sell_open = np.where(tmp, np.minimum(np.zeros_like(pos_t_1), pos_t_1) - pos_t, 0.0)
    ret_ts -= cost * sell_open

    # 买平
    tmp = (pos_t_1 < 0) & (pos_t > pos_t_1)
    buy_close = np.where(tmp, np.minimum(np.zeros_like(pos_t), pos_t) - pos_t_1, 0.0)
    ret_ts -= cost * buy_close

    win_count, loss_count = calc_win_rate(buy_open, sell_close, buy_close, sell_open, return_tadd1)

    # stop_loss = torch.from_numpy(fnc(pos_t.cpu().numpy(), y.cpu().numpy())).to(ret_ts.device)
    # ret_ts = ret_ts - stop_loss * 2 * cost

    ret_ts = ret_ts.sum(axis=-1, keepdims=False)  # (time, n_stocks)
    if not freq.endswith("d"):
        if n_secondary_bars is None:
            raise ValueError("n_secondary_bars must be None if freq is not end with 'd'")
        else:
            ret_ts = ret_ts.reshape(-1, n_secondary_bars, ret_ts.shape[1]).sum(
                axis=1, keepdims=False
            )  # (n_days, n_stocks)

    cum_ret_ts = ret_ts.cumsum(axis=0)
    mdd = (np.maximum.accumulate(cum_ret_ts, axis=0) - cum_ret_ts).max(axis=0)

    res = {}

    res["count"] = np.median(count.sum(axis=0).mean(axis=-1))
    res["total_count"] = np.sum(count.sum(axis=0).mean(axis=-1))
    res["symbol_count"] = np.sum(count.sum(axis=0).mean(axis=-1) > 0)
    t1 = np.sum(loss_count)
    t2 = np.sum(win_count)
    res["total_win_rate"] = t2 / (t1 + t2) if t1 + t2 > 0 else np.nan

    t1 = np.sum(loss_count, axis=-1)
    t2 = np.sum(win_count, axis=-1)
    t1t2 = (t1 + t2) > 0
    if np.sum(t1t2) > 0:
        res["median_win_rate"] = np.median(np.divide(t2[t1t2], (t1 + t2)[t1t2]))
    else:
        res["median_win_rate"] = np.nan

    res["days"] = ret_ts.shape[0]
    res["mdd"] = np.median(mdd)

    # calc sharpe
    mean_ret = ret_ts.mean(axis=0) * 252
    res["positive_return_ratio"] = (mean_ret > 0).mean()
    std_ret = ret_ts.std(axis=0) * np.sqrt(252) + 1e-10
    res["sharpe"] = np.median((mean_ret - 0.02) / std_ret)
    res["arr"] = np.median(mean_ret)

    # calc sortino
    mean_ret = ret_ts.mean(axis=0) * 252 - 0.02
    tmp = np.where(ret_ts < (0.02 / 252), ret_ts, np.full_like(ret_ts, fill_value=np.nan))
    std_ret = np.nansum(np.power(tmp - (0.02 / 252), 2), axis=0) / ret_ts.shape[0] * np.sqrt(252) + 1e-10
    res["sortino"] = np.median(mean_ret / std_ret)

    return Report(**res)


class BackTester:
    def __init__(self, cost: float = 0.0, freq: str = "1d", one_way: int = 0, device: str = "cpu"):
        self.cost = cost
        self.freq = freq
        self.one_way = one_way
        self.device = device

    def __call__(self, positions: torch.Tensor, returns: torch.Tensor, secondary_bars: int):
        return backtest(
            positions, returns, self.cost, freq=self.freq, n_secondary_bars=secondary_bars, one_way=self.one_way
        )
