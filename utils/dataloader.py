import re
from typing import Callable, ClassVar, List, Mapping, Set

import numpy as np
import pandas as pd
import torch


def require(*features):
    def decorator(func):
        def wrapper(df):
            if tmp := set(features).difference(df.columns):
                raise ValueError(f"Missing features: {', '.join(tmp)}")
            return func(df)

        return wrapper

    return decorator


@require("high", "low", "close")
def calc_tr(df):
    return np.maximum(
        np.maximum(
            np.abs(df["high"] - df["close"].shift(1)),
            np.abs(df["low"] - df["close"].shift(1)),
        ),
        np.abs(df["high"] - df["low"]),
    )


@require("volume")
def calc_obv(df):
    return np.where(
        df["close"] > df["close"].shift(1),
        df["volume"],
        np.where(df["close"] < df["close"].shift(1), -df["volume"], 0),
    ).cumsum()


@require("high", "low", "close")
def calc_tp(df):
    return (df["high"] + df["low"] + df["close"]) / 3


@require("high", "low")
def calc_mp(df):
    return (df["high"] + df["low"]) / 2


@require("high", "low", "close")
def calc_wc(df):
    return (df["high"] + df["low"] + df["close"] * 2) / 4


@require("high", "low", "open")
def calc_cfj(df):
    return np.maximum(np.abs(df["high"] - df["open"]), np.abs(df["low"] - df["open"]))


class Dataloader:
    price_featureset: ClassVar[Set] = {
        "open",
        "close",
        "high",
        "low",
        "volume",
        "turnover",
        "turnover_rate",
        "quote_rate",
        "amp_rate",
        "avg_price",
        "prev_close",
    }
    money_featureset: ClassVar[Set] = {"act_buy", "act_sell", "dde_l", "net_flow_rate", "l_net_value"}
    mtss_featureset: ClassVar[Set] = {"fin_buy_value", "sec_sell_value", "fin_value", "sec_value"}
    ashare_featureset: ClassVar[Set] = {"pe_mrq", "pb_mrq", "ps_mrq", "total_mv", "float_mv"}
    thsindex_featureset: ClassVar[Set] = {
        "thsindex1",
        "thsindex2_a",
        "thsindex2_b",
        "thsindex2_c",
        "thsindex3",
        "thsindex4",
        "thsindex4j",
        "thsindex5_a",
        "thsindex5_b",
        "thsindex5_c",
        "thsindex6",
        "thsindex6j",
        "thsindex7",
        "thsindex7j",
        "focus_sticky_1d",
        "focus_sticky_3d",
        "focus_sticky_7d",
        "focus_sticky_30d",
        "heat_1d",
        "heat_7d",
        "heat_30d",
        "heat_focus_1d",
        "heat_focus_7d",
        "heat_focus_30d",
        "heat_search_1d",
        "heat_search_7d",
        "heat_search_30d",
        "heat_zxstk",
        "heat_zxroc_1d",
        "heat_zxroc_7d",
        "heat_zxroc_30d",
    }
    derived_featureset: ClassVar[Mapping[str, Callable]] = {
        "tr": calc_tr,
        "obv": calc_obv,
        "tp": calc_tp,
        "mp": calc_mp,
        "wc": calc_wc,
        "cfj": calc_cfj,
    }

    @classmethod
    def get_open_api(cls):
        from mindgo_api import get_open_api

        if not hasattr(cls, "_open_api"):
            cls._open_api = get_open_api("share:research")
        return cls._open_api

    @classmethod
    def get_raw_data(
        cls,
        index: str,
        start_time: str,
        end_time: str,
        freq: str,
        fields: List[str],
        max_backtrack_bars: int,
        max_future_bars: int,
    ) -> pd.DataFrame:
        """
        features: 为字符串fields小写
        """

        from mgquant_mod_mindgo.research.research_api import get_all_securities
        from mindgo_api import (
            asharevalue,
            get_last_trade_day,
            get_money_flow_step,
            get_mtss,
            get_normal_query,
            get_price,
            query,
        )

        open_api = cls.get_open_api()
        calendar = get_price(
            "000300.SH",
            get_last_trade_day(start_time, -max_backtrack_bars - 1),
            get_last_trade_day(end_time, max_future_bars + 1),
            freq,
            ["open"],
        ).index
        max_date = get_last_trade_day().replace(hour=15)  # 跑训练只需要上个交易日
        start_index = max(calendar.get_indexer([start_time], method="bfill")[0] - max_backtrack_bars, 0)
        end_index = min(calendar.get_indexer([end_time], method="ffill")[0] + max_future_bars, len(calendar) - 1)
        start_time, end_time = calendar[start_index].normalize(), min(calendar[end_index].replace(hour=15), max_date)
        if index == "all":
            securities = sorted(get_all_securities("stock", "nat").query("end_date>'2005'").index.tolist())
        elif re.match(r"^\(.*\)$", index):
            securities = index[1:-1].split(",")
        else:
            securities = sorted(
                {s for x in index.split(",") for s in open_api.range_index_stocks(x, start_time, end_time)["symbol"]}
            )
        fields_set = set(fields)
        pfields = list(fields_set & cls.price_featureset)
        mdf = get_price(
            securities,
            start_date=start_time,
            end_date=end_time,
            fre_step=freq,
            fields=pfields,
            skip_paused=False,
            fq="post",
            is_panel=True,
        ).to_frame()
        pfields = list(fields_set & cls.money_featureset)
        if pfields:
            money_features = [
                "act_buy_xl",
                "act_buy_l",
                "act_buy_m",
                "act_sell_xl",
                "act_sell_l",
                "act_sell_m",
                "dde_l",
                "net_flow_rate",
                "l_net_value",
            ]
            tdf = get_money_flow_step(
                securities,
                start_date=start_time,
                end_date=end_time,
                fre_step=freq,
                fields=money_features,
                is_panel=True,
            ).to_frame()
            tdf["act_buy"] = tdf["act_buy_xl"] + tdf["act_buy_l"]
            tdf["act_sell"] = tdf["act_sell_xl"] + tdf["act_sell_l"]
            mdf = mdf.join(tdf[pfields], how="left")
        pfields = list(fields_set & cls.mtss_featureset)
        if pfields:
            tdf = (
                get_mtss(securities, start_date=start_time, end_date=end_time, fields=pfields, is_panel=True)
                .to_frame()
                .fillna(0)
            )
            mdf = mdf.join(tdf, how="left")
        pfields = list(fields_set & cls.ashare_featureset)
        if pfields:
            q = (
                query(
                    asharevalue.date,
                    asharevalue.symbol,
                    asharevalue.pe_mrq,
                    asharevalue.pb_mrq,
                    asharevalue.ps_mrq,
                    asharevalue.total_mv,
                    asharevalue.float_mv,
                )
                .filter(asharevalue.date >= start_time, asharevalue.date <= end_time)
                .order_by(asharevalue.date)
            )
            tdf = get_normal_query(q)
            tdf.columns = [x[12:] for x in tdf.columns]
            tdf["date"] = pd.to_datetime(tdf["date"])
            tdf = tdf.rename(columns={"date": "major", "symbol": "minor"}).set_index(["major", "minor"])
            mdf = mdf.join(tdf[pfields], how="left")
        pfields = list(fields_set & cls.thsindex_featureset)
        if pfields:
            tdf = open_api.get_hxfactor_thsindex(
                start_time, end_time, pfields, securities if len(securities) < 1000 else None
            )
            tdf = tdf.rename(columns={"date": "major", "symbol": "minor"}).set_index(["major", "minor"])
            mdf = mdf.join(tdf, how="left")
        mdf = mdf.fillna(0 if freq.endswith("m") else np.nan)
        return mdf

    @classmethod
    def prepare_data(
        cls,
        index: str,
        start_time: str,
        end_time: str,
        freq: str,
        fields: List[str],
        max_backtrack_bars: int,
        max_future_bars: int,
    ) -> torch.Tensor:
        try:
            mdf = cls.get_raw_data(index, start_time, end_time, freq, fields, max_backtrack_bars, max_future_bars)
        except ImportError:
            if freq.endswith("m"):
                return torch.randn(100, 240, len(fields), 50) * 2 - 1
            else:
                return torch.randn(1, 4800, len(fields), 50) * 2 - 1

        for field, fnc in cls.derived_featureset.items():
            if field not in fields:
                continue
            mdf[field] = fnc(mdf)

        mdf["date"] = mdf.index.get_level_values("major").normalize()  # 拆分date和time
        mdf["time"] = mdf.index.get_level_values("major").strftime("%H:%M:%S")
        mdf["symbol"] = mdf.index.get_level_values("minor")  # 将3维扩成4维
        mdf = mdf.set_index(["date", "time", "symbol"]).reindex(columns=fields).to_xarray()
        if freq.endswith("d"):
            values = mdf.to_array().to_numpy().transpose(2, 1, 0, 3)  # time, date, field, symbol
        else:
            values = mdf.to_array().to_numpy().transpose(1, 2, 0, 3)  # date, time, field, symbol
        return torch.from_numpy(values).cpu()
