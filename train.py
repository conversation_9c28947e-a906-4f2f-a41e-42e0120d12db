from typing import List

from sb3_contrib import Mask<PERSON>PP<PERSON>

from base.calculator import Calculator
from base.data import Data
from base.pool import Pool
from env.callback import Callback
from env.environment import Env
from env.utils import reseed_everything
from utils.argparser import parse_args


def main(
        name: str,
        index: str,
        start_time: str,
        end_time: str,
        freq: str,

        target: str,
        pool_size: int,
        max_expr_length: int,

        operators: List[str],
        features: List[str],
        constants: List[int],
        bars: List[int],
        steps: int = 2048 * 512,
        device: str = "cuda",
        seed: int = 82
):
    reseed_everything(seed)
    tokens, target, backward_bars, forward_bars = parse_args(
        operators, features, constants, bars, target, max_expr_length
    )
    data = Data(index, features, start_time, end_time, freq, backward_bars, forward_bars,
                step=5, sample_rate=0.2, device="cuda")
    calculator = Calculator(data, target)
    pool = Pool(pool_size, calculator)
    env = Env(pool, tokens, max_expr_length)
    model = MaskablePPO(
        "MlpPolicy", env,
        n_steps=128,
        gamma=1, ent_coef=0.01, batch_size=64, learning_rate=5e-5, n_epochs=4,
        tensorboard_log="logs", device=device, verbose=1
    )
    callback = Callback("out", pool, calculator)
    model.learn(total_timesteps=steps, callback=callback, reset_num_timesteps=False, tb_log_name=name)


if __name__ == '__main__':
    main(
        "default", "", "", "", "1d",
        "DeltaRatio($close, -5)", 10, 10,
        ["Neg", "Add", "FiniteCond", "RollingMean", "Corr"],
        ["OPEN", "CLOSE", "HIGH", "LOW"],
        [10, 5, 3, 2, 1, 0.5, 0.1, 0.01],
        [1, 2, 3, 5, 10],
    )
